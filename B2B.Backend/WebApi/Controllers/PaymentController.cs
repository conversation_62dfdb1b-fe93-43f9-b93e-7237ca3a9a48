using Application.Contracts.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Payments.Iyzico.Models;

namespace WebApi.Controllers;

[ApiController]
[Route("api/[controller]")]
public class PaymentController : ControllerBase
{
    private readonly IPaymentService _paymentService;
    private readonly IOrderService _orderService;

    public PaymentController(IPaymentService paymentService, IOrderService orderService)
    {
        _paymentService = paymentService;
        _orderService = orderService;
    }

    [HttpPost("iyzico/initialize")]
    public async Task<ActionResult> InitializeIyzicoPayment([FromBody] object request)
    {
        try
        {
            // Sipariş bilgilerini doğrula
            InitializePaymentRequest paymentRequest = new InitializePaymentRequest();
            // İyzico için ödeme başlat

            return Ok();
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPost("iyzico/callback")]
    public async Task<ActionResult> IyzicoCallback([FromBody] string request)
    {
        try
        {

            return Ok();
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }
}
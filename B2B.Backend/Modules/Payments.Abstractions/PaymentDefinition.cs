using Core.Entities;

namespace Payments.Abstraction;

/// <summary>
/// Kargo firması tanımı - BaseEntity'den türer
/// Her kargo modülü bu sınıftan türetilmiş bir Definition.cs dosyası içerir
/// </summary>
public abstract class PaymentDefinition : BaseEntity
{
    /// <summary>
    /// Kargo firması adı (örn: "Yurtiçi Kargo")
    /// </summary>
    public abstract string Name { get; }

    /// <summary>
    /// Kısa kod (örn: "YURTICI", "MNG", "ARAS")
    /// </summary>
    public abstract string ShortCode { get; }

    /// <summary>
    /// Açıklama
    /// </summary>
    public abstract string Description { get; }

    /// <summary>
    /// API anahtarı gerekli mi?
    /// </summary>
    public abstract bool RequiresApiKey { get; }

    /// <summary>
    /// API URL gerekli mi?
    /// </summary>
    public abstract bool RequiresApiUrl { get; }

    /// <summary>
    /// Varsay<PERSON>lan ayarlar (JSON formatında)
    /// </summary>
    public abstract Dictionary<string, string> DefaultSettings { get; }

    /// <summary>
    /// Logo URL'i (opsiyonel)
    /// </summary>
    public abstract string? LogoUrl { get; }

}

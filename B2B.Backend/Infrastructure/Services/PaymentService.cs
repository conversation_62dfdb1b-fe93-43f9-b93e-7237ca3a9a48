using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Core.Entities;
using Core.Enums;
using Core.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Services;

public class PaymentService : IPaymentService
{
    private readonly IGenericRepository<Payment> _paymentRepository;
    private readonly IGenericRepository<Order> _orderRepository;

    public PaymentService(
        IGenericRepository<Payment> paymentRepository,
        IGenericRepository<Order> orderRepository)
    {
        _paymentRepository = paymentRepository;
        _orderRepository = orderRepository;
    }

    public async Task<List<PaymentListDto>> GetListAsync(int? page = null, int? pageSize = null)
    {
        var query = _paymentRepository.Query()
            .Include(p => p.Order)
                .ThenInclude(o => o.Customer)
            .Where(p => !p.IsDeleted)
            .OrderByDescending(p => p.CreatedAt);

        if (page.HasValue && pageSize.HasValue)
        {
            query = (IOrderedQueryable<Payment>)query.Skip((page.Value - 1) * pageSize.Value).Take(pageSize.Value);
        }

        var payments = await query.ToListAsync();

        return payments.Select(p => new PaymentListDto
        {
            Id = p.Id,
            OrderId = p.OrderId,
            OrderNumber = p.Order.OrderNumber,
            CustomerName = p.Order.Customer.NameSurname,
            Amount = p.Amount,
            PaymentMethod = p.PaymentMethod,
            Status = p.Status,
            PaymentResponseTransactionId = p.PaymentResponseTransactionId,
            CreatedAt = p.CreatedAt
        }).ToList();
    }

    public async Task<PaymentDto?> GetByIdAsync(Guid id)
    {
        var payment = await _paymentRepository.Query()
            .Include(p => p.Order)
                .ThenInclude(o => o.Customer)
            .FirstOrDefaultAsync(p => p.Id == id && !p.IsDeleted);

        if (payment == null) return null;

        return new PaymentDto
        {
            Id = payment.Id,
            OrderId = payment.OrderId,
            Amount = payment.Amount,
            Description = payment.Description,
            PaymentMethod = payment.PaymentMethod,
            Status = payment.Status,
            PaymentResponse = payment.PaymentResponse,
            PaymentResponseCode = payment.PaymentResponseCode,
            PaymentResponseMessage = payment.PaymentResponseMessage,
            PaymentResponseTransactionId = payment.PaymentResponseTransactionId,
            CreatedAt = payment.CreatedAt,
            UpdatedAt = payment.UpdatedAt,
            OrderNumber = payment.Order.OrderNumber,
            CustomerName = payment.Order.Customer.NameSurname
        };
    }

    public async Task<List<PaymentListDto>> GetByOrderIdAsync(Guid orderId)
    {
        var payments = await _paymentRepository.Query()
            .Include(p => p.Order)
                .ThenInclude(o => o.Customer)
            .Where(p => p.OrderId == orderId && !p.IsDeleted)
            .OrderByDescending(p => p.CreatedAt)
            .ToListAsync();

        return payments.Select(p => new PaymentListDto
        {
            Id = p.Id,
            OrderId = p.OrderId,
            OrderNumber = p.Order.OrderNumber,
            CustomerName = p.Order.Customer.NameSurname,
            Amount = p.Amount,
            PaymentMethod = p.PaymentMethod,
            Status = p.Status,
            PaymentResponseTransactionId = p.PaymentResponseTransactionId,
            CreatedAt = p.CreatedAt
        }).ToList();
    }

    public async Task<List<PaymentListDto>> GetByStatusAsync(PaymentStatus status)
    {
        var payments = await _paymentRepository.Query()
            .Include(p => p.Order)
                .ThenInclude(o => o.Customer)
            .Where(p => p.Status == status && !p.IsDeleted)
            .OrderByDescending(p => p.CreatedAt)
            .ToListAsync();

        return payments.Select(p => new PaymentListDto
        {
            Id = p.Id,
            OrderId = p.OrderId,
            OrderNumber = p.Order.OrderNumber,
            CustomerName = p.Order.Customer.NameSurname,
            Amount = p.Amount,
            PaymentMethod = p.PaymentMethod,
            Status = p.Status,
            PaymentResponseTransactionId = p.PaymentResponseTransactionId,
            CreatedAt = p.CreatedAt
        }).ToList();
    }

    public async Task<List<PaymentListDto>> SearchPaymentsAsync(string searchTerm)
    {
        var payments = await _paymentRepository.Query()
            .Include(p => p.Order)
                .ThenInclude(o => o.Customer)
            .Where(p => !p.IsDeleted &&
                       (p.Order.OrderNumber.Contains(searchTerm) ||
                        p.Order.Customer.NameSurname.Contains(searchTerm) ||
                        p.PaymentResponseTransactionId.Contains(searchTerm) ||
                        p.PaymentMethod.Contains(searchTerm)))
            .OrderByDescending(p => p.CreatedAt)
            .ToListAsync();

        return payments.Select(p => new PaymentListDto
        {
            Id = p.Id,
            OrderId = p.OrderId,
            OrderNumber = p.Order.OrderNumber,
            CustomerName = p.Order.Customer.NameSurname,
            Amount = p.Amount,
            PaymentMethod = p.PaymentMethod,
            Status = p.Status,
            PaymentResponseTransactionId = p.PaymentResponseTransactionId,
            CreatedAt = p.CreatedAt
        }).ToList();
    }

    public async Task<Guid> CreateAsync(PaymentCreateDto dto)
    {
        // Verify order exists
        var order = await _orderRepository.GetByIdAsync(dto.OrderId);
        if (order == null || order.IsDeleted)
            throw new ArgumentException("Order not found");

        var payment = new Payment
        {
            Id = Guid.CreateVersion7(),
            OrderId = dto.OrderId,
            Amount = dto.Amount,
            Description = dto.Description,
            PaymentMethod = dto.PaymentMethod,
            Status = dto.Status,
            PaymentResponse = dto.PaymentResponse,
            PaymentResponseCode = dto.PaymentResponseCode,
            PaymentResponseMessage = dto.PaymentResponseMessage,
            PaymentResponseTransactionId = dto.PaymentResponseTransactionId,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await _paymentRepository.AddAsync(payment);
        return payment.Id;
    }

    public async Task UpdateAsync(PaymentUpdateDto dto)
    {
        var payment = await _paymentRepository.GetByIdAsync(dto.Id);
        if (payment == null || payment.IsDeleted)
            throw new ArgumentException("Payment not found");

        payment.Amount = dto.Amount;
        payment.Description = dto.Description;
        payment.PaymentMethod = dto.PaymentMethod;
        payment.Status = dto.Status;
        payment.PaymentResponse = dto.PaymentResponse;
        payment.PaymentResponseCode = dto.PaymentResponseCode;
        payment.PaymentResponseMessage = dto.PaymentResponseMessage;
        payment.PaymentResponseTransactionId = dto.PaymentResponseTransactionId;
        payment.UpdatedAt = DateTime.UtcNow;

        _paymentRepository.Update(payment);
        await _paymentRepository.SaveChangesAsync();
    }

    public async Task UpdateStatusAsync(PaymentStatusUpdateDto dto)
    {
        var payment = await _paymentRepository.GetByIdAsync(dto.Id);
        if (payment == null || payment.IsDeleted)
            throw new ArgumentException("Payment not found");

        payment.Status = dto.Status;
        if (!string.IsNullOrEmpty(dto.PaymentResponse))
            payment.PaymentResponse = dto.PaymentResponse;
        if (!string.IsNullOrEmpty(dto.PaymentResponseCode))
            payment.PaymentResponseCode = dto.PaymentResponseCode;
        if (!string.IsNullOrEmpty(dto.PaymentResponseMessage))
            payment.PaymentResponseMessage = dto.PaymentResponseMessage;
        if (!string.IsNullOrEmpty(dto.PaymentResponseTransactionId))
            payment.PaymentResponseTransactionId = dto.PaymentResponseTransactionId;
        payment.UpdatedAt = DateTime.UtcNow;

        _paymentRepository.Update(payment);
        await _paymentRepository.SaveChangesAsync();
    }

    public async Task DeleteAsync(Guid id)
    {
        var payment = await _paymentRepository.GetByIdAsync(id);
        if (payment == null || payment.IsDeleted)
            throw new ArgumentException("Payment not found");

        payment.IsDeleted = true;
        payment.UpdatedAt = DateTime.UtcNow;

        _paymentRepository.Update(payment);
        await _paymentRepository.SaveChangesAsync();
    }

    public async Task<decimal> GetTotalPaymentsAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        var query = _paymentRepository.Query()
            .Where(p => !p.IsDeleted && p.Status == PaymentStatus.Completed);

        if (startDate.HasValue)
            query = query.Where(p => p.CreatedAt >= startDate.Value);

        if (endDate.HasValue)
            query = query.Where(p => p.CreatedAt <= endDate.Value);

        return await query.SumAsync(p => p.Amount);
    }

    public async Task<int> GetPaymentCountAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        var query = _paymentRepository.Query()
            .Where(p => !p.IsDeleted);

        if (startDate.HasValue)
            query = query.Where(p => p.CreatedAt >= startDate.Value);

        if (endDate.HasValue)
            query = query.Where(p => p.CreatedAt <= endDate.Value);

        return await query.CountAsync();
    }

    public async Task<decimal> GetTotalPaymentsByStatusAsync(PaymentStatus status, DateTime? startDate = null, DateTime? endDate = null)
    {
        var query = _paymentRepository.Query()
            .Where(p => !p.IsDeleted && p.Status == status);

        if (startDate.HasValue)
            query = query.Where(p => p.CreatedAt >= startDate.Value);

        if (endDate.HasValue)
            query = query.Where(p => p.CreatedAt <= endDate.Value);

        return await query.SumAsync(p => p.Amount);
    }
}

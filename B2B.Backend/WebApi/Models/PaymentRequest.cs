using System.ComponentModel.DataAnnotations;

namespace WebApi.Models;

public class PaymentRequest
{
    [Required]
    public ShippingAddressDto ShippingAddress { get; set; }
    
    [Required]
    public BillingAddressDto BillingAddress { get; set; }
    
    [Required]
    public ShippingInfoDto ShippingInfo { get; set; }
    
    [Required]
    public List<CartProductDto> CartProducts { get; set; }
    
    [Required]
    public decimal TotalPrice { get; set; }
    
    public Guid? CustomerId { get; set; }
}

public class ShippingAddressDto
{
    public Guid Id { get; set; }
    public string Name { get; set; }
    public string Line1 { get; set; }
    public string Line2 { get; set; }
    public string City { get; set; }
    public string District { get; set; }
    public string Country { get; set; }
    public string PostalCode { get; set; }
    public int AddressType { get; set; }
    public bool IsDefault { get; set; }
}

public class BillingAddressDto
{
    public Guid Id { get; set; }
    public string Name { get; set; }
    public string Line1 { get; set; }
    public string Line2 { get; set; }
    public string City { get; set; }
    public string District { get; set; }
    public string Country { get; set; }
    public string PostalCode { get; set; }
    public int AddressType { get; set; }
    public bool IsDefault { get; set; }
}

public class ShippingInfoDto
{
    [Required]
    public string FirstName { get; set; }
    
    [Required]
    public string LastName { get; set; }
    
    [Required]
    public string Address { get; set; }
    
    [Required]
    public string City { get; set; }
    
    public string ZipCode { get; set; }
    
    [Required]
    public string Phone { get; set; }
    
    [Required]
    [EmailAddress]
    public string Email { get; set; }
}

public class CartProductDto
{
    public Guid Id { get; set; }
    public string Name { get; set; }
    public decimal Price { get; set; }
    public int Quantity { get; set; }
    public string Image { get; set; }
    public decimal? OldPrice { get; set; }
}

public class PaymentInitializationResult
{
    public bool IsSuccess { get; set; }
    public string Message { get; set; }
    public string PaymentPageUrl { get; set; }
    public string Token { get; set; }
    public Guid? OrderId { get; set; }
    public object Data { get; set; }
}

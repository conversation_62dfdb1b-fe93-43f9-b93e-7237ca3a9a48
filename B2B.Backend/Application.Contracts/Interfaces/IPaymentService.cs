using Application.Contracts.DTOs;
using Core.Enums;
using WebApi.Models;

namespace Application.Contracts.Interfaces;

public interface IPaymentService
{
    Task<List<PaymentListDto>> GetListAsync(int? page = null, int? pageSize = null);
    Task<PaymentDto?> GetByIdAsync(Guid id);
    Task<List<PaymentListDto>> GetByOrderIdAsync(Guid orderId);
    Task<List<PaymentListDto>> GetByStatusAsync(PaymentStatus status);
    Task<List<PaymentListDto>> SearchPaymentsAsync(string searchTerm);
    Task<Guid> CreateAsync(PaymentCreateDto dto);
    Task UpdateAsync(PaymentUpdateDto dto);
    Task UpdateStatusAsync(PaymentStatusUpdateDto dto);
    Task DeleteAsync(Guid id);
    
    // Analytics methods
    Task<decimal> GetTotalPaymentsAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<int> GetPaymentCountAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<decimal> GetTotalPaymentsByStatusAsync(PaymentStatus status, DateTime? startDate = null, DateTime? endDate = null);

    // Iyzico Payment methods
    Task<PaymentInitializationResult> InitializeIyzicoPaymentAsync(PaymentRequest request);
}

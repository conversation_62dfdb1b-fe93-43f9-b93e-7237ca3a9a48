"use client";

import { useContextElement } from "@/context/Context";
import Link from "next/link";
import { formatTLPrice } from "@/utils/currency";
import Image from "next/image";
export default function Checkout() {
  const {
    cartProducts,

    totalPrice,
  } = useContextElement();

  return (
    <div className="flat-spacing-25">
      <div className="container">
        <div className="row">
          <div className="col-xl-8">
            <form className="tf-checkout-cart-main">
              <div className="box-ip-checkout">
                <div className="title text-xl fw-medium">Kargo Bilgileriniz</div>
                <div className="grid-2 mb_16">
                  <div className="tf-field style-2 style-3">
                    <input
                      className="tf-field-input tf-input"
                      id="firstname"
                      placeholder=" "
                      type="text"
                      name="firstname"
                    />
                    <label className="tf-field-label" htmlFor="firstname">
                      Adı<PERSON>ız
                    </label>
                  </div>
                  <div className="tf-field style-2 style-3">
                    <input
                      className="tf-field-input tf-input"
                      id="lastname"
                      placeholder=" "
                      type="text"
                      name="lastname"
                    />
                    <label className="tf-field-label" htmlFor="lastname">
                      Soyadınız
                    </label>
                  </div>
                </div>
                {/* <fieldset className="tf-field style-2 style-3 mb_16"> */}
                {/*   <input */}
                {/*     className="tf-field-input tf-input" */}
                {/*     id="country" */}
                {/*     type="text" */}
                {/*     name="country" */}
                {/*     placeholder="" */}
                {/*   /> */}
                {/*   <label className="tf-field-label" htmlFor="country"> */}
                {/*     Country */}
                {/*   </label> */}
                {/* </fieldset> */}
                <fieldset className="tf-field style-2 style-3 mb_16">
                  <input
                    className="tf-field-input tf-input"
                    id="address"
                    type="text"
                    name="address"
                    placeholder=""
                  />
                  <label className="tf-field-label" htmlFor="address">
                    Adresiniz
                  </label>
                </fieldset>
                {/* <fieldset className="mb_16"> */}
                {/*   <input */}
                {/*     type="text" */}
                {/*     className="style-2" */}
                {/*     name="apartment" */}
                {/*     placeholder="Apartman Numarası" */}
                {/*   /> */}
                {/* </fieldset> */}
                <div className="grid-3 mb_16">
                  <fieldset className="tf-field style-2 style-3">
                    <input
                      className="tf-field-input tf-input"
                      id="city"
                      type="text"
                      name="city"
                      placeholder=""
                    />
                    <label className="tf-field-label" htmlFor="city">
                      Şehir
                    </label>
                  </fieldset>
                  <fieldset className="tf-field style-2 style-3">
                    <input
                      className="tf-field-input tf-input"
                      id="code"
                      type="text"
                      name="zipcode"
                      placeholder=""
                    />
                    <label className="tf-field-label" htmlFor="code">
                      Posta Kodu
                    </label>
                  </fieldset>
                </div>
                <fieldset className="tf-field style-2 style-3 mb_16">
                  <input
                    className="tf-field-input tf-input"
                    id="phone"
                    type="text"
                    name="phone"
                    placeholder=""
                  />
                  <label className="tf-field-label" htmlFor="phone">
                    Telefon Numaranız (05998887766)
                  </label>
                </fieldset>
                <fieldset className="tf-field style-2 style-3 mb_16">
                  <input
                    className="tf-field-input tf-input"
                    id="email"
                    type="text"
                    name="email"
                    placeholder=""
                  />
                  <label className="tf-field-label" htmlFor="email">
                    E-posta Adresiniz
                  </label>
                </fieldset>
              </div>
              <div className="box-ip-payment">
                <div className="title">
                  <div className="text-lg fw-medium mb_4">Ödeme</div>
                  <p className="text-sm text-main">
                    Ödemeler İyzico ile güvenli ve şifrelenmiştir.
                  </p>
                </div>
                <div className="payment-method-box" id="payment-method-box">
                  <div className="payment-item mb_16">
                    <label
                      htmlFor="credit-card"
                      className="payment-header"
                      data-bs-toggle="collapse"
                      data-bs-target="#credit-card-payment"
                      aria-controls="credit-card-payment"
                    >
                      <input
                        type="radio"
                        name="payment-method"
                        className="tf-check-rounded"
                        id="credit-card"
                        defaultChecked=""
                      />
                      <span className="pay-title text-sm">İyzico</span>
                    </label>
                    <div
                      id="credit-card-payment"
                      className="collapse show"
                      data-bs-parent="#payment-method-box"
                    >
                      <div className="payment-body">
                        <fieldset className="ip-card mb_16">
                          <input
                            type="text"
                            className="style-2"
                            placeholder="Card number"
                          />
                          <Image
                            className="card-logo"
                            width={41}
                            height={12}
                            alt="card"
                            src="/images/payment/visa-2.png"
                          />
                        </fieldset>
                        <div className="grid-2 mb_16">
                          <input
                            type="text"
                            className="style-2"
                            placeholder="Expiration date (MM/YY)"
                          />
                          <input
                            type="text"
                            className="style-2"
                            placeholder="Sercurity code"
                          />
                        </div>
                        <fieldset className="mb_16">
                          <input
                            type="text"
                            className="style-2"
                            placeholder="Name on card"
                          />
                        </fieldset>
                        <div className="cb-ship">
                          <input
                            type="checkbox"
                            defaultChecked=""
                            className="tf-check"
                            id="checkShip"
                          />
                          <label
                            htmlFor="checkShip"
                            className="text-sm text-main"
                          >
                            Use shipping address as billing address
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="payment-item paypal-payment mb_16">
                    <label
                      htmlFor="paypal"
                      className="payment-header collapsed"
                      data-bs-toggle="collapse"
                      data-bs-target="#paypal-payment"
                      aria-controls="paypal-payment"
                    >
                      <input
                        type="radio"
                        name="payment-method"
                        className="tf-check-rounded"
                        id="paypal"
                      />
                      <span className="pay-title text-sm">
                        PayPal
                        <Image
                          className="card-logo"
                          width={78}
                          height={20}
                          alt="apple"
                          src="/images/payment/paypal-2.png"
                        />
                      </span>
                    </label>
                    <div
                      id="paypal-payment"
                      className="collapse"
                      data-bs-parent="#payment-method-box"
                    />
                  </div>
                </div>
                <p className="text-dark-6 text-sm">
                  Your personal data will be used to process your order, support
                  your experience throughout this website, and for other
                  purposes described in our
                  <Link
                    href={`/privacy-policy`}
                    className="fw-medium text-decoration-underline link text-sm"
                  >
                    privacy policy.
                  </Link>
                </p>
              </div>
            </form>
          </div>
          <div className="col-xl-4">
            <div className="tf-page-cart-sidebar">
              <form action="thank-you.html" className="cart-box order-box">
                <div className="title text-lg fw-medium">Sepetiniz</div>
                {cartProducts.length ? (
                  <ul className="list-order-product">
                    {cartProducts.map((product, i) => (
                      <li key={i} className="order-item">
                        <figure className="img-product">
                          <Image
                            alt="product"
                            src={product.image}
                            width={144}
                            height={188}
                          />
                          <span className="quantity">{product.quantity}</span>
                        </figure>
                        <div className="content">
                          <div className="info">
                            <p className="name text-sm fw-medium">
                              {product.name}
                            </p>
                          </div>
                          <span className="price text-sm fw-medium">
                            {formatTLPrice(product.price * product.quantity)}
                          </span>
                        </div>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <div className="p-4">
                    Sepetiniz Boş! Alışverişe başlamak için ürünlere göz atın.{" "}
                    <Link
                      className="tf-btn btn-dark2 animate-btn mt-3"
                      href="/urunler"
                    >
                      Ürünleri Keşfet
                    </Link>
                  </div>
                )}
                <ul className="list-total">
                  <li className="total-item text-sm d-flex justify-content-between">
                    <span>Vergiler Hariç Toplam:</span>
                    <span className="price-sub fw-medium">
                      {formatTLPrice(totalPrice / 1.2)}
                    </span>
                  </li>
                  <li className="total-item text-sm d-flex justify-content-between">
                    <span>İndirim:</span>
                    <span className="price-discount fw-medium">
                      {totalPrice ? `${formatTLPrice(0)}` : formatTLPrice(0)}
                    </span>
                  </li>
                  <li className="total-item text-sm d-flex justify-content-between">
                    <span>Kargo:</span>
                    <span className="price-ship fw-medium">
                      Ücretsiz {/* {totalPrice ? formatTLPrice(10) : formatTLPrice(0)} */}
                    </span>
                  </li>
                  <li className="total-item text-sm d-flex justify-content-between">
                    <span>Vergi (KDV):</span>
                    <span className="price-tax fw-medium">
                      {totalPrice ? formatTLPrice(totalPrice - totalPrice / 1.2) : formatTLPrice(0)}
                    </span>
                  </li>
                </ul>
                <div className="subtotal text-lg fw-medium d-flex justify-content-between">
                  <span>Subtotal:</span>
                  <span className="total-price-order">
                    {" "}
                    {totalPrice ? formatTLPrice(totalPrice) : formatTLPrice(0)}
                  </span>
                </div>
                <div className="btn-order">
                  <button
                    type="submit"
                    className="tf-btn btn-dark2 animate-btn w-100"
                  >
                    Siparişi Tamamla
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

namespace Payments.Iyzico;

/// <summary>
/// İyzico ödeme tanımı
/// </summary>
public class IyzicoDefinition 
{
    public string Name => "Iyzico";
    public string ShortCode => "IYZICO";
    public string Description => "İyzico ile kolay ve güvenli ödeme";
    public bool RequiresApiKey => true;
    public bool RequiresApiUrl => true;
    public string? LogoUrl => "/images/payments/iyzico-logo.png";

    public Dictionary<string, string> DefaultSettings => new()
    {
        { "ApiUrl", "https://api.yurtici.com.tr" },
        { "TestMode", "true" },
        { "Timeout", "30" },
        { "MaxRetries", "3" },
        { "SmsNotification", "true" },
        { "EmailNotification", "true" }
    };
}

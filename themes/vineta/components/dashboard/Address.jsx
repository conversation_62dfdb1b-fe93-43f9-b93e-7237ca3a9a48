"use client";
import React, { useState, useEffect } from "react";
import Sidebar from "./Sidebar";
import { accountRequests } from '@/services/account.js'; // API servis dosyanız

// Varsayılan boş adres formu state'i (DTO'ya uygun)
const initialAddressState = {
  name: "",
  line1: "",
  line2: "",
  city: "",
  district: "",
  country: "Türkiye",
  postalCode: "",
  addressType: 0, // Billing=0, Shipping=1
  isDefault: false,
};

export default function Address({ customerId }) { // customerId'nin prop olarak geldiğini varsayıyoruz
  const [addresses, setAddresses] = useState([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingAddressId, setEditingAddressId] = useState(null);
  const [newAddress, setNewAddress] = useState(initialAddressState);
  const [loading, setLoading] = useState(true); // Veri yükleme durumunu takip etmek için

  // Adresleri API'den çekmek için
  const fetchAddresses = async () => {
    if (!customerId) return;
    try {
      setLoading(true);
      const response = await accountRequests.getAddresses(customerId);
      // Backend'den gelen verinin AddressListDto formatında olduğunu varsayıyoruz
      // Ancak düzenleme için tüm alanlara ihtiyacımız var. 
      // API'nin getAddresses endpoint'inin AddressDto gibi detaylı bir model döndürdüğünü varsayıyoruz.
      setAddresses(response || []);
    } catch (error) {
      console.error("Adresler getirilirken hata oluştu:", error);
      // Burada kullanıcıya bir hata mesajı gösterebilirsiniz.
    } finally {
      setLoading(false);
    }
  };

  // Bileşen yüklendiğinde ve customerId değiştiğinde adresleri çek
  useEffect(() => {
    fetchAddresses();
  }, [customerId]);

  // Form input değişikliklerini yönet
  const handleInputChange = (e) => {
    const { id, value, type, checked } = e.target;

    let finalValue = value; // Değeri önce bir değişkene alalım

    if (type === "checkbox") {
      finalValue = checked;
    } else if (id === "addressType") {
      // Eğer değişiklik addressType alanından geliyorsa, değeri sayıya çevir
      finalValue = parseInt(value, 10);
    }

    setNewAddress((prevAddress) => ({
      ...prevAddress,
      [id]: finalValue,
    }));
  };

  // Yeni Adres Ekleme
  const handleAddAddress = async (e) => {
    e.preventDefault();
    try {
      const payload = { ...newAddress, customerId }; // AddressCreateDto'ya uygun payload
      console.log(payload);
      await accountRequests.addAddress(payload);
      await fetchAddresses(); // Listeyi güncelle
      setShowAddForm(false);
      setNewAddress(initialAddressState); // Formu temizle
    } catch (error) {
      console.error("Adres eklenirken bir hata oluştu:", error);
    }
  };

  // Adres Düzenleme Formunu Açma
  const handleEditAddress = (id) => {
    const addressToEdit = addresses.find((addr) => addr.id === id);
    if (addressToEdit) {
      // Formu doldurmak için AddressUpdateDto'ya uygun hale getiriyoruz
      setNewAddress({
        name: addressToEdit.name,
        line1: addressToEdit.line1,
        line2: addressToEdit.line2 || "",
        city: addressToEdit.city,
        district: addressToEdit.district,
        country: addressToEdit.country || "Türkiye",
        postalCode: addressToEdit.postalCode || "",
        addressType: addressToEdit.addressType,
        isDefault: addressToEdit.isDefault,
      });
      setEditingAddressId(id);
      setShowAddForm(false); // Ekleme formu açıksa kapat
    }
  };

  // Adres Güncelleme
  const handleUpdateAddress = async (e) => {
    e.preventDefault();
    try {
      const payload = { ...newAddress, id: editingAddressId }; // AddressUpdateDto
      await accountRequests.updateAddress(payload);
      await fetchAddresses(); // Listeyi güncelle
      setEditingAddressId(null);
      setNewAddress(initialAddressState);
    } catch (error) {
      console.error("Adres güncellenirken bir hata oluştu:", error);
    }
  };

  // Adres Silme
  const handleDeleteAddress = async (id) => {
    if (window.confirm("Bu adresi silmek istediğinizden emin misiniz?")) {
      try {
        await accountRequests.removeAddress(id);
        await fetchAddresses(); // Listeyi güncelle
      } catch (error) {
        console.error("Adres silinirken bir hata oluştu:", error);
      }
    }
  };

  // Düzenleme veya ekleme formunu iptal et
  const handleCancel = () => {
    setShowAddForm(false);
    setEditingAddressId(null);
    setNewAddress(initialAddressState);
  };

  const renderAddressForm = (handleSubmit, buttonText, cancelText) => (
    <form
      onSubmit={handleSubmit}
      className="wd-form-address form-default"
      style={{ display: "block", marginTop: "20px", marginBottom: "20px" }}
    >
      <fieldset style={{ marginTop: "16px" }}>
        <label htmlFor="addressType" style={{ marginTop: "8px" }}>Adres Tipi</label>
        <div className="tf-select">
          <select id="addressType" value={newAddress.addressType} onChange={handleInputChange} required>
            <option value={0}>Fatura Adresi</option>
            <option value={1}>Teslimat Adresi</option>
          </select>
        </div>
      </fieldset>
      <fieldset style={{ marginTop: "16px" }}>
        <label htmlFor="name" style={{ marginTop: "8px" }}>Adres Adı</label>
        <input type="text" id="name" value={newAddress.name} onChange={handleInputChange} required />
      </fieldset>
      <fieldset style={{ marginTop: "16px" }}>
        <label htmlFor="line1" style={{ marginTop: "8px" }}>Adres Satırı 1</label>
        <input type="text" id="line1" value={newAddress.line1} onChange={handleInputChange} required />
      </fieldset>
      <fieldset style={{ marginTop: "16px" }}>
        <label htmlFor="line2" style={{ marginTop: "8px" }}>Adres Satırı 2 (İsteğe Bağlı)</label>
        <input type="text" id="line2" value={newAddress.line2} onChange={handleInputChange} />
      </fieldset>
      <div className="cols-2">
        <fieldset style={{ marginTop: "16px" }}>
          <label htmlFor="district" style={{ marginTop: "8px" }}>İlçe</label>
          <input type="text" id="district" value={newAddress.district} onChange={handleInputChange} required />
        </fieldset>
        <fieldset style={{ marginTop: "16px" }}>
          <label htmlFor="city" style={{ marginTop: "8px" }}>İl (Şehir)</label>
          <input type="text" id="city" value={newAddress.city} onChange={handleInputChange} required />
        </fieldset>
      </div>
      <div className="cols-2">
        <fieldset style={{ marginTop: "16px" }}>
          <label htmlFor="country" style={{ marginTop: "8px" }}>Ülke</label>
          <input type="text" id="country" value={newAddress.country} onChange={handleInputChange} required />
        </fieldset>
        <fieldset style={{ marginTop: "16px" }}>
          <label htmlFor="postalCode" style={{ marginTop: "8px" }}>Posta Kodu (İsteğe Bağlı)</label>
          <input type="text" id="postalCode" value={newAddress.postalCode} onChange={handleInputChange} />
        </fieldset>
      </div>
      <div className="tf-cart-checkbox" style={{ marginTop: "16px" }}>
        <input type="checkbox" className="tf-check" id="isDefault" checked={newAddress.isDefault} onChange={handleInputChange} />
        <label htmlFor="isDefault" className="label"><span>Varsayılan adres olarak ayarla</span></label>
      </div>
      <div className="box-btn" style={{ marginTop: "24px" }}>
        <button className="tf-btn animate-btn" type="submit">{buttonText}</button>
        <button type="button" className="tf-btn btn-out-line-dark" onClick={handleCancel}>{cancelText}</button>
      </div>
    </form>
  );

  return (
    <div className="flat-spacing-13">
      <div className="container-7">
        <div className="btn-sidebar-mb d-lg-none">
          <button data-bs-toggle="offcanvas" data-bs-target="#mbAccount">
            <i className="icon icon-sidebar" />
          </button>
        </div>
        <div className="main-content-account">
          <div className="sidebar-account-wrap sidebar-content-wrap sticky-top d-lg-block d-none">
            <ul className="my-account-nav">
              <Sidebar />
            </ul>
          </div>
          <div className="my-acount-content account-address">
            <h6 className="title-account">
              Adresleriniz ({addresses.length})
            </h6>
            <div className="widget-inner-address">
              {!showAddForm && editingAddressId === null && (
                <button
                  className="tf-btn btn-add-address animate-btn"
                  onClick={() => { setShowAddForm(true); setEditingAddressId(null); setNewAddress(initialAddressState); }}
                >
                  Yeni adres ekle
                </button>
              )}

              {showAddForm && renderAddressForm(handleAddAddress, "Adres Ekle", "İptal")}
              {editingAddressId !== null && renderAddressForm(handleUpdateAddress, "Güncelle", "İptal")}

              {loading ? (
                <p>Adresler yükleniyor...</p>
              ) : (
                <ul className="list-account-address tf-grid-layout md-col-2">
                  {addresses.length ? addresses.map((address) => (
                    <li className="account-address-item" key={address.id}>
                      <p className="title text-md fw-medium">
                        {address.name} {address.isDefault && "(Varsayılan)"}
                      </p>
                      <div className="info-detail">
                        <div className="box-infor">
                          {/* AddressListDto'daki 'fullAddress' kullanılıyor */}
                          <p className="text-md">{address.line1} {address.line2}</p>
                          <p className="text-md">{address.district} / {address.city}</p>
                          <p className="text-md fw-bold">{address.addressTypeName}</p>
                        </div>
                        <div className="box-btn">
                          <button
                            className="tf-btn btn-out-line-dark btn-edit-address"
                            onClick={() => handleEditAddress(address.id)}
                          >
                            Düzenle
                          </button>
                          <button
                            className="tf-btn btn-out-line-dark btn-delete-address"
                            onClick={() => handleDeleteAddress(address.id)}
                          >
                            Sil
                          </button>
                        </div>
                      </div>
                    </li>
                  )) : (
                    <p>Kayıtlı adresiniz bulunmamaktadır. Yeni adres ekleyerek siparişlerinizi kolaylıkla tamamlayabilirsiniz.</p>
                  )}
                </ul>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
